/* Font Definitions */
@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-300.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-600.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Lora';
    src: url('assets/fonts/Lora-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Lora';
    src: url('assets/fonts/Lora-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Caveat';
    src: url('assets/fonts/caveat-v18-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Caveat';
    src: url('assets/fonts/caveat-v18-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    /* Optimize scrolling performance */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Lora', serif;
    line-height: 1.6;
    color: #333;
    background-color: #222222;
    /* Optimize scrolling performance */
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
}

/* Unified Heading System - Consistent styling for all headings */
h1, h2, h3, h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #333;
    margin: 20px 0 15px 0;
    line-height: 1.3;
}

h1 {
    font-size: 32px;
    color: #222;
    margin: 30px 0 20px 0;
}

h2 {
    font-size: 28px;
    color: #fbc99a;
    margin: 25px 0 18px 0;
}

h3 {
    font-size: 18px;
    color: #333;
    margin: 20px 0 10px 0;
}

h4 {
    font-size: 16px;
    color: #333;
    margin: 15px 0 8px 0;
}



/* Banner */
.banner {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: scroll;
    height: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
}

.banner h2 {
    color: #fbc99a;
    font-size: 2.5rem;
    z-index: 1;
    position: relative;
    text-shadow:
        0 0 10px rgba(34, 34, 34, 0.9),
        0 0 20px rgba(34, 34, 34, 0.7),
        2px 2px 4px rgba(34, 34, 34, 0.8);
    margin: 0;
}

.banner .einheitsbutton {
    z-index: 1;
    position: relative;
}

/* Banner content wrapper for perfect centering */
.banner-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    z-index: 1;
    position: relative;
}

.banner-zwischenmenschlichkeit {
    background-image: url('assets/bilder/zwischenmenschlichkeit.png');
    background-size: cover !important;
    background-position: center 30% !important;
}

/* Desktop-spezifische Positionierung für Zwischenmenschlichkeit Banner */
@media (min-width: 769px) {
    .banner-zwischenmenschlichkeit {
        background-position: center 45% !important;
    }
}

.banner-zusammenhalt {
    background-image: url('assets/bilder/zusammenhalt.png');
}

.banner-fuehrung {
    background-image: url('assets/bilder/zusammenhalt.png');
}

.banner-blumenfeld {
    background-image: url('assets/bilder/blumenfeld.png');
}

.banner-weekend {
    background-image: url('assets/bilder/weekend.png');
}

.banner-zukunftinsjetzt {
    background-image: url('assets/bilder/zukunftinsjetzt.png');
}

/* Desktop-Styles für Banner-Titel */
@media (min-width: 769px) {
    .mobile-hero-title {
        display: none !important;
    }

    .desktop-subtitle {
        display: block !important;
        font-size: 2.5rem;
        color: #fbc99a;
        z-index: 1;
        position: relative;
        text-shadow:
            0 0 10px rgba(34, 34, 34, 0.9),
            0 0 20px rgba(34, 34, 34, 0.7),
            2px 2px 4px rgba(34, 34, 34, 0.8);
        margin: 0;
    }

    .banner-zukunftinsjetzt::after {
        display: none;
    }
}


/* Unified Section Box - Beautiful consistent styling for all sections */
.section-box {
    max-width: 800px;
    margin: 40px auto;
    padding: 30px;
    background: #222222;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(251, 201, 154, 0.3);
    line-height: 1.8;
    position: relative;
    text-align: center; /* Zentriere alle Texte */
}

.section-box p {
    margin-bottom: 15px;
    font-size: 16px;
    color: #fbc99a;
}

/* Reduziere Abstand zwischen Überschriften und nachfolgenden Listen */
/* Browserkompatible Lösung ohne :has() */
.section-box p.list-header {
    margin-bottom: 5px;
}

/* Reduziere oberen Abstand der Listen nach list-header */
.section-box p.list-header + ul,
.section-box p.list-header + ol {
    margin-top: 5px;
}

.section-box strong {
    font-weight: 700;
    color: #fbc99a;
}

.section-box h3 {
    text-align: center; /* Überschriften auch zentriert */
    color: #fbc99a;
    font-size: 20px;
    margin: 45px 0 20px 0; /* Mehr Abstand vor den Überschriften */
    font-weight: 600;
}

.section-box ul {
    margin: 20px 0;
    padding-left: 0;
    text-align: center; /* Aufzählungen zentriert */
    display: block; /* Volle Breite für zentrierte Darstellung */
    list-style: none;
}

.section-box ol {
    margin: 20px 0;
    padding-left: 0;
    text-align: center; /* Nummerierte Listen zentriert */
    display: block; /* Volle Breite für zentrierte Darstellung */
    list-style: none;
}

.section-box li {
    margin-bottom: 15px;
    font-size: 16px;
    color: #fbc99a;
    text-align: center;
    position: relative;
    padding: 0;
}

/* Modern list styling for Co-Creation Week */
.possibilities-list {
    list-style: none;
    padding-left: 0;
    text-align: left;
    display: block;
}

.possibilities-list li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 10px;
    line-height: 1.6;
    text-align: left;
}

.possibilities-list li::before {
    content: "✦";
    position: absolute;
    left: 0;
    top: 0;
    color: #fbc99a;
    font-size: 16px;
}

/* Accordion Styling - angepasst für Events Section */
.accordion-container {
    margin: 20px auto;
    max-width: 800px;
}

.accordion-item {
    margin-bottom: 2px;
    border-radius: 6px;
    overflow: hidden;
    background: #fff8ef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(4, 7, 15, 0.08);
}

.accordion-header {
    display: flex;
    align-items: center;
    padding: 20px 25px;
    cursor: pointer;
    background: transparent;
    transition: all 0.3s ease;
    user-select: none;
    border: none;
}

.accordion-header:hover {
    background: rgba(4, 7, 15, 0.05);
}

.accordion-header.active {
    background: rgba(4, 7, 15, 0.08);
}

.day-number {
    font-weight: 300;
    color: #04070f;
    min-width: 120px;
    font-size: 16px;
    letter-spacing: 2px;
    text-align: left;
}

.day-focus {
    flex: 1;
    margin-left: 20px;
    font-weight: 500;
    color: #04070f;
    font-size: 15px;
    text-align: left;
}

.accordion-icon {
    font-size: 18px;
    font-weight: bold;
    color: #04070f;
    transition: transform 0.3s ease;
    min-width: 25px;
    text-align: center;
}

.accordion-header.active .accordion-icon {
    transform: rotate(45deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: rgba(4, 7, 15, 0.02);
}

.accordion-content.active {
    max-height: 250px;
}

.accordion-content p {
    padding: 25px;
    margin: 0;
    line-height: 1.7;
    color: #04070f;
    font-size: 15px;
    text-align: left;
}

.facts-list {
    list-style: none;
    padding-left: 0;
    text-align: left;
    display: inline-block; /* Macht die Liste nur so breit wie nötig und zentriert sie */
}

.facts-list li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 8px;
    line-height: 1.6;
    text-align: left;
}

.facts-list li::before {
    content: "▸";
    position: absolute;
    left: 0;
    top: 0;
    color: #04070f;
    font-size: 16px;
    font-weight: bold;
}

/* Modern Slideshow Container */
.slideshow-container {
    position: relative;
    max-width: 800px;
    margin: 20px auto;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.slideshow-wrapper {
    position: relative;
    width: 100%;
    height: 300px; /* Feste Höhe für alle Slides */
    overflow: hidden;
    touch-action: pan-y pinch-zoom;
}

.slides-track {
    display: flex;
    width: 700%; /* 7 Slides * 100% */
    height: 100%;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide {
    flex: 0 0 calc(100% / 7); /* Jeder Slide nimmt 1/7 der Breite */
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

/* Versteckte externe Navigation */
.slideshow-nav {
    display: none;
}

/* Interne Navigation im Textkästchen */
.slide-nav-left,
.slide-nav-right {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: rgba(255, 248, 239, 0.1);
    border: 1px solid rgba(255, 248, 239, 0.2);
    border-radius: 50%;
    color: rgba(255, 248, 239, 0.6);
    font-size: 18px;
    font-weight: 300;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.slide-nav-left {
    left: 15px;
}

.slide-nav-right {
    right: 15px;
}

.slide-nav-left:hover,
.slide-nav-right:hover {
    background: rgba(255, 248, 239, 0.2);
    color: rgba(255, 248, 239, 0.9);
    border-color: rgba(255, 248, 239, 0.4);
}

/* Dezente Indikatoren im Textkästchen */
.slide-indicators-internal {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    z-index: 10;
}

.indicator-internal {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: rgba(255, 248, 239, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator-internal.active {
    background: rgba(255, 248, 239, 0.8);
    transform: scale(1.3);
}

.indicator-internal:hover:not(.active) {
    background: rgba(255, 248, 239, 0.6);
}

/* Highlight Boxes - Modern Design mit fester Höhe */
.highlight-box {
    background: linear-gradient(135deg, #04070f 0%, #29303b 100%);
    padding: 30px;
    margin: 0;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.highlight-box p {
    margin: 0;
    line-height: 1.6;
    text-align: left;
    color: #fff8ef !important;
    font-size: 16px;
}

.highlight-box strong {
    color: #fff8ef !important;
    font-weight: 600;
    display: block;
    margin-bottom: 12px;
    font-size: 18px;
}

/* Facts Box - für praktische Informationen - angepasst für Events Section */
.facts-box {
    background: linear-gradient(135deg, #fff8ef 0%, #29303b 100%);
    padding: 25px;
    margin: 20px auto;
    border-radius: 8px;
    box-shadow: none;
    max-width: 800px;
}

.facts-box .facts-list {
    margin: 0;
}

.facts-list li {
    color: #04070f;
}

.facts-list li::before {
    color: #04070f;
}

/* Possibilities Box - für "Was du erleben kannst" Liste - angepasst für Events Section */
.possibilities-box {
    background: linear-gradient(135deg, #fff8ef 0%, #29303b 100%);
    padding: 25px;
    margin: 20px auto;
    border-radius: 8px;
    box-shadow: none;
    max-width: 800px;
}

.possibilities-box .possibilities-list {
    margin: 0;
}

.possibilities-list li {
    color: #04070f;
}

.possibilities-list li::before {
    color: #04070f;
}

/* Principles Boxes - für Schwarmverbindung Prinzipien */
.principles-box {
    background: rgba(251, 201, 154, 0.05);
    border: 2px solid rgba(251, 201, 154, 0.3);
    padding: 25px;
    margin: 20px 0;
    border-radius: 12px;
}

.principles-box p {
    margin-bottom: 15px;
    line-height: 1.7;
}

.principles-box p:last-child {
    margin-bottom: 0;
}

/* Kompakte Flip Cards - 2x4 Grid Layout */
.flip-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    margin: 20px 0;
}

.flip-card-compact {
    background-color: transparent;
    width: 100%;
    height: 160px;
    perspective: 1000px;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.5s;
    transform-style: preserve-3d;
    cursor: pointer;
}

.flip-card-compact:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 12px;
    box-sizing: border-box;
}

/* Flip Cards - angepasst für dunklen Hintergrund */
.zwischenmenschlichkeit-content-section .flip-card-front {
    background: rgba(251, 201, 154, 0.1);
    border: 1px solid rgba(251, 201, 154, 0.3);
    color: #fbc99a;
}

.zwischenmenschlichkeit-content-section .flip-card-back {
    background: rgba(251, 201, 154, 0.15);
    border: 1px solid rgba(251, 201, 154, 0.4);
    color: #fbc99a;
    transform: rotateY(180deg);
}

.zwischenmenschlichkeit-content-section .flip-card-front h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 10px;
    font-weight: 600;
    color: #fbc99a;
    margin: 0;
    line-height: 1.1;
    text-align: center;
}

.zwischenmenschlichkeit-content-section .flip-card-back p {
    font-size: 12px;
    line-height: 1.3;
    margin: 0;
    color: #fbc99a;
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

/* Standard Flip Cards für andere Bereiche */
.flip-card-front {
    background: rgba(251, 201, 154, 0.1);
    border: 1px solid rgba(251, 201, 154, 0.3);
    color: #fbc99a;
}

.flip-card-back {
    background: rgba(251, 201, 154, 0.15);
    border: 1px solid rgba(251, 201, 154, 0.4);
    color: #fbc99a;
    transform: rotateY(180deg);
}

.flip-card-front h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 10px;
    font-weight: 600;
    color: #fbc99a;
    margin: 0;
    line-height: 1.1;
    text-align: center;
}

.flip-card-back p {
    font-size: 12px;
    line-height: 1.3;
    margin: 0;
    color: #fbc99a;
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

/* h4 Styling für Prinzipien-Kategorien */
.section-box h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 18px;
    font-weight: 600;
    color: #fbc99a;
    text-align: center;
    margin: 35px 0 15px 0;
}

.section-box h4:first-of-type {
    margin-top: 25px;
}

/* Call to Action Box - poetischer, inspirierender Stil */
.call-to-action-box {
    background: linear-gradient(135deg, rgba(251, 201, 154, 0.15), rgba(251, 201, 154, 0.08));
    border: none;
    padding: 35px;
    margin: 25px 0;
    border-radius: 20px;
    text-align: center;
    position: relative;
    box-shadow: 0 8px 25px rgba(251, 201, 154, 0.2);
}

/* Peach Card - Pfirsichfarbene Karte */
.peach-card {
    background: #fbd19a;
    padding: 30px;
    margin: 25px auto;
    border-radius: 15px;
    max-width: 800px;
    box-shadow: 0 8px 25px rgba(251, 209, 154, 0.3);
    text-align: center;
    position: relative;
}

.peach-card p {
    margin-bottom: 15px;
    line-height: 1.7;
    font-size: 16px;
    color: #38160e;
}

.peach-card p:last-child {
    margin-bottom: 0;
}

.peach-card strong {
    color: #38160e;
    font-weight: 600;
}

.call-to-action-box::before {
    content: "✦";
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 20px;
    color: #fbc99a;
    opacity: 0.7;
}

.call-to-action-box p {
    margin-bottom: 20px;
    line-height: 1.8;
    font-size: 16px;
}

.call-to-action-box p:last-child {
    margin-bottom: 0;
}

.call-emphasis {
    font-size: 20px !important;
    color: #fbc99a !important;
    margin: 25px 0 !important;
    font-family: 'Montserrat', sans-serif !important;
    font-weight: 600 !important;
    letter-spacing: 1px;
}

.call-final {
    font-style: italic !important;
    font-size: 17px !important;
    margin-top: 25px !important;
    color: #fbc99a !important;
}

/* Community Section - Neues Design ohne Box */
.community-section {
    background-color: #04070f;
    padding: 60px 20px;
    margin: 0;
    width: 100%;
}

.section-main-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #fbc99a;
    text-align: center;
    margin: 0 auto 30px auto;
    max-width: 800px;
    letter-spacing: 1px;
    line-height: 1.2;
}

.community-content {
    max-width: 800px;
    margin: 0 auto;
    color: #fff8ef;
    line-height: 1.8;
    text-align: center;
}

.community-content p {
    margin-bottom: 20px;
    font-size: 16px;
    color: #fff8ef;
}

.letter-body {
    margin-bottom: 30px;
}

.letter-body p {
    margin-bottom: 20px;
    line-height: 1.8;
    color: #fff8ef;
}

.letter-action {
    text-align: center;
    margin-top: 30px;
}

/* Brief-Styling für andere Bereiche */
.letter-greeting {
    margin-bottom: 25px;
}

.letter-greeting p {
    font-size: 18px;
    font-style: italic;
    color: #fff8ef;
    font-weight: 500;
}

.letter-vision {
    margin: 25px 0;
}

.letter-vision p {
    margin-bottom: 18px;
    line-height: 1.7;
}

.letter-emphasis {
    font-weight: 600;
    color: #fff8ef;
}

.letter-signature {
    margin: 30px 0 25px 0;
    text-align: center;
    font-style: italic;
    color: #fff8ef;
}

/* Community Section - now uses general centered styling from .section-box */

/* Profile elements in section-box - centered styling */
.section-box .profile-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    border: 2px solid #fbc99a;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
    margin: 0 auto 20px auto;
    display: block;
}

.section-box .profile-image:hover {
    transform: scale(1.05);
}



/* Center social links in section-box */
.section-box .social-links {
    text-align: center !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Center einheitsbutton for social links */
.social-links .einheitsbutton {
    margin: 0 !important;
    display: inline-block !important;
    width: auto !important;
    flex-shrink: 0;
}

/* Letter Closing Banner/Pre-Footer */
.letter-closing-banner {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #04070f 0%, #29303b 100%);
    position: relative;
    margin-top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}

/* Dekoratives Favicon-Element am oberen Rand */
.letter-closing-banner::before {
    content: "";
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: #04070f url('assets/favicon.png') center/contain no-repeat;
    border-radius: 50%;
    border: 2px solid #fbc99a;
    box-shadow: 0 0 15px rgba(251, 201, 154, 0.5);
    z-index: 10;
}

.letter-closing-banner .closing-content {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 40px;
    align-items: center;
    max-width: 800px;
    width: 100%;
}

.letter-closing-banner .closing-profile-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
    justify-self: center;
}

.letter-closing-banner .closing-profile-image:hover {
    transform: scale(1.05);
}

.letter-closing-banner .closing-text {
    text-align: center;
}

.letter-closing-banner .closing-text .letter-signature {
    font-size: 28px;
    font-style: italic;
    color: #fbc99a;
    margin-bottom: 25px;
    font-weight: 300;
}

.letter-closing-banner .closing-text h3 {
    color: #fbc99a;
    font-size: 22px;
    margin: 0 0 25px 0;
    font-weight: 600;
}

.letter-closing-banner .closing-text .social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* Social Media Buttons für hellen Letter Closing Banner */
.letter-closing-banner .einheitsbutton.instagram {
    background-color: transparent !important;
    border: 2px solid #e91e63 !important;
    color: #e91e63 !important;
}

.letter-closing-banner .einheitsbutton.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) !important;
    border: 2px solid transparent !important;
    color: white !important;
    transform: scale(1.05);
}

.letter-closing-banner .einheitsbutton.spotify {
    background-color: transparent !important;
    border-color: #1DB954 !important;
    color: #1DB954 !important;
}

.letter-closing-banner .einheitsbutton.spotify:hover {
    background-color: #1DB954 !important;
    border-color: #1DB954 !important;
    color: #222222 !important;
    transform: scale(1.05);
}

.letter-closing-banner .einheitsbutton.telegram {
    background-color: transparent !important;
    border-color: #0088cc !important;
    color: #0088cc !important;
}

.letter-closing-banner .einheitsbutton.telegram:hover {
    background-color: #0088cc !important;
    border-color: #0088cc !important;
    color: #222222 !important;
    transform: scale(1.05);
}

/* Einheitsbutton - Unified button styling for all buttons */
.einheitsbutton {
    display: block;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    margin: auto;
    width: fit-content;
    text-align: center;
    background-color: #fbc99a;
    color: #222222;
    border: 2px solid #fbc99a;
}

/* Standard hover animation - color inversion + scale */
.einheitsbutton:hover {
    background-color: #222222;
    color: #fbc99a;
    border-color: #fbc99a;
    transform: scale(1.05);
}

/* Inactive extension - overrides default active state */
.einheitsbutton.inactive {
    background-color: #fbd19a;
    color: #04070f;
    border: 2px solid #fbd19a;
    opacity: 1;
    pointer-events: none;
    cursor: not-allowed;
}

.einheitsbutton.inactive:hover {
    background-color: #fbd19a;
    color: #04070f;
    border: 2px solid #fbd19a;
    transform: none;
}

/* Transparent extension - for banner buttons */
.einheitsbutton.transparent {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.einheitsbutton.transparent:hover {
    background-color: white;
    color: #333;
    border-color: white;
    transform: scale(1.05);
}

/* Events Text - simple text without box styling */
.events-text {
    max-width: 800px;
    margin: 40px auto 20px auto;
    padding: 0 30px;
    text-align: center;
    line-height: 1.8;
}

.events-text p {
    margin-bottom: 15px;
    font-size: 16px;
}

/* Events Grid Container - separate container for better width utilization */
.events-grid-container {
    max-width: 1200px;
    margin: 20px auto 40px auto;
    padding: 0 30px;
}

.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    justify-content: center;
    max-width: 100%;
}

/* Limit to maximum 3 columns */
@media (min-width: 1020px) {
    .events-grid {
        grid-template-columns: repeat(3, minmax(300px, 380px));
    }
}

/* For very wide screens, allow even more width */
@media (min-width: 1400px) {
    .events-grid {
        grid-template-columns: repeat(3, minmax(300px, 420px));
    }
}

.event-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    /* Optimize for scrolling performance */
    will-change: auto;
}

.event-card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.event-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.event-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}


.event-description {
    font-size: 14px;
    margin-bottom: 15px;
    color: #666;
    flex-grow: 1;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 18px;
    margin-top: 25px;
    flex-wrap: nowrap;
    align-items: center;
    overflow: hidden;
}

/* Instagram extension - special gradient styling */
.einheitsbutton.instagram {
    border: 2px solid transparent;
    background: linear-gradient(#222222, #222222) padding-box,
                linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) border-box;
    color: #e6683c;
}

.einheitsbutton.instagram:hover {
    border: 2px solid transparent;
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) padding-box,
                linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) border-box;
    color: white;
    transform: scale(1.05);
}

/* YouTube extension - red styling */
.einheitsbutton.youtube {
    background-color: #222222;
    border-color: #FF0000;
    color: #FF0000;
}

.einheitsbutton.youtube:hover {
    background-color: #FF0000;
    border-color: #FF0000;
    color: #222222;
    transform: scale(1.05);
}

/* Spotify extension - green styling */
.einheitsbutton.spotify {
    background-color: #222222;
    border-color: #1DB954;
    color: #1DB954;
}

.einheitsbutton.spotify:hover {
    background-color: #1DB954;
    border-color: #1DB954;
    color: #222222;
    transform: scale(1.05);
}

/* Telegram extension - blue styling */
.einheitsbutton.telegram {
    background-color: #222222;
    border-color: #0088cc;
    color: #0088cc;
}

.einheitsbutton.telegram:hover {
    background-color: #0088cc;
    border-color: #0088cc;
    color: #222222;
    transform: scale(1.05);
}

/* Events Image Section - Horizontales Bild mit Farbwechsel */
.events-image-section {
    position: relative;
    width: 100%;
    margin: 0;
    padding: 0;
    background: linear-gradient(to bottom, #04070f 50%, #fff8ef 50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.events-horizontal-image {
    width: 100%;
    max-width: 800px;
    height: 300px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.events-title {
    color: #fbd19a !important;
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    box-shadow: none;
    max-width: 800px;
    margin: 0 20px;
    text-shadow: none;
}

/* Events Content Section - Weißer Hintergrund */
.events-content-section {
    background-color: #fff8ef;
    padding: 60px 20px;
    margin: 0;
    width: 100%;
}

.events-content-section p {
    max-width: 800px;
    margin: 0 auto 20px auto;
    color: #04070f;
    font-size: 16px;
    line-height: 1.8;
    text-align: center;
}

.events-content-section h3 {
    max-width: 800px;
    margin: 40px auto 20px auto;
    color: #04070f;
    text-align: center;
}

.events-content-section h3:first-child {
    margin-top: 0;
}

.events-content-section strong {
    color: #04070f;
}

/* Zwischenmenschlichkeit Image Section - Horizontales Bild mit Farbwechsel */
.zwischenmenschlichkeit-image-section {
    position: relative;
    width: 100%;
    margin: 0;
    padding: 0;
    background: linear-gradient(to bottom, #fff8ef 50%, #04070f 50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.zwischenmenschlichkeit-horizontal-image {
    width: 100%;
    max-width: 800px;
    height: 300px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.zwischenmenschlichkeit-title {
    color: #fbd19a !important;
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    box-shadow: none;
    max-width: 800px;
    margin: 0 20px;
    text-shadow: none;
}

/* Zwischenmenschlichkeit Content Section - Dunkler Hintergrund */
.zwischenmenschlichkeit-content-section {
    background-color: #04070f;
    padding: 30px 20px 60px 20px;
    margin: 0;
    width: 100%;
}

.zwischenmenschlichkeit-content-section p {
    max-width: 800px;
    margin: 0 auto 20px auto;
    color: #fff8ef;
    font-size: 16px;
    line-height: 1.8;
    text-align: center;
}

.zwischenmenschlichkeit-content-section h3 {
    max-width: 800px;
    margin: 40px auto 20px auto;
    color: #fbd19a;
    text-align: center;
}

.zwischenmenschlichkeit-content-section h3:first-child {
    margin-top: 0;
}

.zwischenmenschlichkeit-content-section strong {
    color: #fbd19a;
}

/* Community Button Section - nach Flipcards */
.community-button-section {
    text-align: center;
    margin-top: 40px;
    padding: 20px 0;
}

.community-button-section .einheitsbutton {
    display: inline-block;
    margin: 0 auto;
}

/* Weekend Page Specific Styles */
.highlights-box {
    background-color: #f8f9fa;
    border-left: 4px solid #fbc99a;
    padding: 25px;
    margin: 30px 0;
    border-radius: 5px;
}

.signature {
    text-align: right;
    margin-top: 40px;
    font-style: italic;
    color: #fbc99a;
    font-size: 18px;
}

.signature a {
    color: #fbc99a;
    text-decoration: none;
    font-style: italic;
}

.signature a:hover {
    color: #fbc99a;
    text-decoration: none;
}

/* Impressum Page Specific Styles - now uses section-box styling */
.section-box h2 {
    margin-bottom: 30px;
    border-bottom: 2px solid #fbc99a;
    padding-bottom: 10px;
}







/* Scrolling Performance Optimizations */
* {
    -webkit-tap-highlight-color: transparent;
}

/* Smooth scrollbar styling for better UX */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(251, 201, 154, 0.6);
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(251, 201, 154, 0.8);
}

/* Mobile-specific optimizations - disable hover effects for better performance */
@media (hover: none) and (pointer: coarse) {
    .einheitsbutton:hover,
    .event-card:hover,
    .profile-image:hover {
        transform: none !important;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
        background-color: inherit !important;
    }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    /* Prevent horizontal overflow on mobile */
    html, body {
        overflow-x: hidden !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    /* Unified heading system - mobile adjustments */
    h1 {
        font-size: 26px;
    }

    h2 {
        font-size: 24px;
    }

    h3 {
        font-size: 16px;
    }

    h4 {
        font-size: 14px;
    }

    .banner h2 {
        font-size: 1.8rem;
    }

    .section-box {
        margin: 20px;
        padding: 20px;
    }

    /* Community Section Mobile */
    .community-section {
        padding: 40px 15px;
    }

    .section-main-title {
        font-size: 1.8rem;
        margin-bottom: 30px;
        padding: 0 10px;
        line-height: 1.3;
    }

    .community-content {
        padding: 0 5px;
    }

    /* Events Section Mobile */
    .events-image-section {
        min-height: 350px;
        padding: 20px 15px;
    }

    .events-horizontal-image {
        height: 200px;
        margin-bottom: 20px;
    }

    .events-title {
        font-size: 1.6rem;
        padding: 15px 20px;
        margin: 0 10px;
        line-height: 1.3;
    }

    .events-content-section {
        padding: 40px 15px;
    }

    /* Slideshow Mobile */
    .slideshow-container {
        margin: 15px auto;
        border-radius: 8px;
    }

    .slideshow-wrapper {
        height: 280px;
    }

    .slide-nav-left,
    .slide-nav-right {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }

    .slide-nav-left {
        left: 12px;
    }

    .slide-nav-right {
        right: 12px;
    }

    .slide-indicators-internal {
        bottom: 15px;
    }

    .indicator-internal {
        width: 5px;
        height: 5px;
    }

    .highlight-box {
        padding: 25px;
        border-radius: 8px;
    }

    .highlight-box strong {
        font-size: 16px;
        margin-bottom: 10px;
    }

    .highlight-box p {
        font-size: 15px;
    }

    /* Zwischenmenschlichkeit Section Mobile */
    .zwischenmenschlichkeit-image-section {
        min-height: 350px;
        padding: 20px 15px;
    }

    .zwischenmenschlichkeit-horizontal-image {
        height: 200px;
        margin-bottom: 20px;
    }

    .zwischenmenschlichkeit-title {
        font-size: 1.6rem;
        padding: 0;
        margin: 0 10px;
        line-height: 1.3;
    }

    .zwischenmenschlichkeit-content-section {
        padding: 20px 15px 40px 15px;
    }

    .events-text {
        margin: 20px;
        padding: 0 20px;
    }

    .events-grid {
        grid-template-columns: 1fr;
    }

    .banner {
        height: 300px;
    }

    /* Mobile-specific banner positioning for better image cropping */
    .banner.banner-zusammenhalt {
        background-size: 200%;
        background-position: center 55%;
    }

    /* Mobile Hero Banner - Vollbild mit zentriertem Text */
    .banner-zukunftinsjetzt {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        position: relative !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .banner-zukunftinsjetzt::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }

    /* Mobile Hero Title - SCHWARMVERBUNDEN */
    .mobile-hero-title {
        display: block !important;
        font-size: clamp(1.2rem, 6vw, 2.5rem) !important;
        color: #fbd19a !important;
        margin: 0 20px 5px 20px !important;
        padding: 0 10px !important;
        text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8) !important;
        z-index: 2 !important;
        position: relative !important;
        font-family: 'Montserrat', sans-serif !important;
        font-weight: 600 !important;
        letter-spacing: clamp(0.3px, 0.5vw, 1.5px) !important;
        text-align: center !important;
        width: calc(100% - 40px) !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        white-space: nowrap !important;
    }

    /* Desktop Subtitle - verstecken auf mobil */
    .desktop-subtitle {
        display: none !important;
    }

    /* Untertitel für mobile Hero Banner */
    .banner-zukunftinsjetzt::after {
        content: "CO-KREATION FÜR LIGHTWORKER";
        position: absolute;
        z-index: 2;
        color: #fff8ef;
        font-family: 'Montserrat', sans-serif;
        font-size: 1.1rem;
        font-weight: 400;
        text-align: center;
        text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
        top: 55%;
        left: 50%;
        transform: translateX(-50%);
        letter-spacing: 0.5px;
        width: calc(100% - 40px);
        max-width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
        word-wrap: break-word;
    }

    .section-box .profile-image {
        width: 150px;
        height: 150px;
    }

    /* Mobile responsiveness for letter closing banner */
    .letter-closing-banner {
        height: 450px; /* Mehr Höhe für bessere Verteilung */
    }

    .letter-closing-banner::before {
        width: 35px;
        height: 35px;
        top: -17px;
    }

    .letter-closing-banner {
        padding: 60px 20px 40px 20px; /* Mehr Padding oben wegen Favicon */
    }

    .letter-closing-banner .closing-content {
        grid-template-columns: 1fr;
        gap: 30px; /* Mehr Abstand zwischen Elementen */
        text-align: center;
        padding: 0;
    }

    .letter-closing-banner .closing-profile-image {
        width: 140px;
        height: 140px;
        justify-self: center;
        margin-top: 20px; /* Abstand zum Favicon */
    }

    .letter-closing-banner .closing-text .letter-signature {
        font-size: 22px;
        margin-bottom: 20px;
    }

    .letter-closing-banner .closing-text h3 {
        font-size: 18px;
        margin-bottom: 20px;
    }

    .letter-closing-banner .closing-text .social-links {
        gap: 8px;
        margin-bottom: 20px; /* Abstand zum unteren Rand */
    }

    .social-links {
        flex-direction: row;
        align-items: flex-start;
        gap: 18px;
    }

    .social-links .einheitsbutton {
        font-size: 12px !important;
        padding: 8px 15px !important;
        flex-shrink: 1;
        min-width: 0;
    }

    .highlights-box,
    .details-box,
    .participation-box {
        margin: 20px 0;
        padding: 20px;
    }
}

@media (max-width: 480px) {
    /* Unified heading system - small mobile adjustments */
    h1 {
        font-size: 22px;
    }

    h2 {
        font-size: 20px;
    }

    h3 {
        font-size: 14px;
    }

    h4 {
        font-size: 13px;
    }

    .banner h2 {
        font-size: 1.5rem;
    }

    .section-box {
        margin: 10px;
        padding: 15px;
    }

    /* Community Section für sehr kleine Bildschirme */
    .community-section {
        padding: 30px 10px;
    }

    .section-main-title {
        font-size: 1.5rem;
        margin-bottom: 25px;
        padding: 0 5px;
    }

    /* Events Section für sehr kleine Bildschirme */
    .events-image-section {
        min-height: 300px;
        padding: 15px 10px;
    }

    .events-horizontal-image {
        height: 150px;
        margin-bottom: 15px;
    }

    .events-title {
        font-size: 1.3rem;
        padding: 12px 15px;
        margin: 0 5px;
        line-height: 1.2;
    }

    .events-content-section {
        padding: 30px 10px;
    }

    /* Slideshow für sehr kleine Bildschirme */
    .slideshow-container {
        margin: 10px auto;
        border-radius: 6px;
    }

    .slideshow-wrapper {
        height: 260px;
    }

    .slide-nav-left,
    .slide-nav-right {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .slide-nav-left {
        left: 10px;
    }

    .slide-nav-right {
        right: 10px;
    }

    .slide-indicators-internal {
        bottom: 12px;
    }

    .indicator-internal {
        width: 4px;
        height: 4px;
    }

    .highlight-box {
        padding: 20px;
        border-radius: 6px;
    }

    .highlight-box strong {
        font-size: 15px;
        margin-bottom: 8px;
    }

    .highlight-box p {
        font-size: 14px;
        line-height: 1.5;
    }

    /* Zwischenmenschlichkeit Section für sehr kleine Bildschirme */
    .zwischenmenschlichkeit-image-section {
        min-height: 300px;
        padding: 15px 10px;
    }

    .zwischenmenschlichkeit-horizontal-image {
        height: 150px;
        margin-bottom: 15px;
    }

    .zwischenmenschlichkeit-title {
        font-size: 1.3rem;
        padding: 0;
        margin: 0 5px;
        line-height: 1.2;
    }

    .zwischenmenschlichkeit-content-section {
        padding: 15px 10px 30px 10px;
    }

    .events-text {
        margin: 10px;
        padding: 0 15px;
    }

    .banner {
        height: 250px;
    }

    /* Enhanced mobile banner positioning for very small screens */
    .banner.banner-zusammenhalt {
        background-size: 220%;
        background-position: center 50%;
    }

    /* Mobile Hero Banner für kleine Bildschirme - Vollbild */
    .banner-zukunftinsjetzt {
        height: 100vh !important;
        background-size: cover !important;
        background-position: center !important;
    }

    .mobile-hero-title {
        font-size: clamp(1rem, 5vw, 1.8rem) !important;
        color: #fbd19a !important;
        letter-spacing: clamp(0.2px, 0.4vw, 1px) !important;
        margin: 0 15px 5px 15px !important;
        padding: 0 5px !important;
        width: calc(100% - 30px) !important;
    }

    .banner-zukunftinsjetzt::after {
        font-size: 0.9rem !important;
        top: 53% !important;
        width: calc(100% - 30px) !important;
        padding: 0 15px !important;
        letter-spacing: 0.3px !important;
    }

    .social-links .einheitsbutton {
        font-size: 11px !important;
        padding: 6px 12px !important;
    }

    /* Small mobile responsiveness for letter closing banner */
    .letter-closing-banner {
        height: 400px; /* Mehr Höhe für sehr kleine Bildschirme */
    }

    .letter-closing-banner::before {
        width: 30px;
        height: 30px;
        top: -15px;
    }

    .letter-closing-banner {
        padding: 50px 15px 30px 15px; /* Angepasstes Padding */
    }

    .letter-closing-banner .closing-content {
        gap: 25px;
        padding: 0;
    }

    .letter-closing-banner .closing-profile-image {
        width: 110px;
        height: 110px;
        margin-top: 15px; /* Abstand zum Favicon */
    }

    .letter-closing-banner .closing-text .letter-signature {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .letter-closing-banner .closing-text h3 {
        font-size: 16px;
        margin-bottom: 15px;
    }

    .letter-closing-banner .closing-text .social-links {
        gap: 6px;
        margin-bottom: 15px; /* Abstand zum unteren Rand */
    }

    .letter-closing-banner .closing-text .social-links .einheitsbutton {
        font-size: 11px !important;
        padding: 6px 10px !important; /* Kleinere Buttons */
    }

    /* Mobile optimizations for flip cards */
    .flip-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        margin: 15px 0;
    }

    .flip-card-compact {
        height: 140px;
    }

    .flip-card-front, .flip-card-back {
        padding: 10px;
    }

    .flip-card-front h4 {
        font-size: 9px;
        line-height: 1.0;
    }

    .flip-card-back p {
        font-size: 11px;
        line-height: 1.2;
        -webkit-line-clamp: 7;
        line-clamp: 7;
    }
}